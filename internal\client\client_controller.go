package client

import (
	handler "tp1/internal/client/handlers"
	cs_protocol "tp1/pkg/protocol/client_server_protocol"
	"tp1/pkg/protocol/table_types"

	"github.com/op/go-logging"
)

type ClientController struct {
	id          string
	connHandler *handler.ConnectionHandler
	dsManager   *handler.DataSetManager
	resHandler  *handler.ResultsHandler
	logger      *logging.Logger
	maxRows     int
}

// NewClientController initializes a dataSetManager and establishes the connection with the server
func NewClientController(path, address string, logger *logging.Logger, maxRows int) (*ClientController, error) {

	// Read and check integrity of DataSet
	dsManager, err := handler.NewDataSetManager(path)
	if err != nil {
		return nil, err
	}

	resHandler, err := handler.NewResultsHandler(path + "/results")
	if err != nil {
		return nil, err
	}

	// Tries to connect to the server, acquiring an UUID
	connHandler, err := handler.NewConnectionHandler(address)
	if err != nil {
		return nil, err
	}

	return &ClientController{
		id:          connHandler.GetClientId(),
		dsManager:   dsManager,
		connHandler: connHandler,
		logger:      logger,
		resHandler:  resHandler,
		maxRows:     maxRows,
	}, nil
}

// Start is the method that starts the transaccion with the server
//
// Sends all the batches of all the tables in the dataset and awaits for the responses
func (c *ClientController) Start() error {
	orderTables := []table_types.TableType{
		table_types.MENU,
		table_types.STORES,
		table_types.USERS,
		table_types.TRANSACTIONS,
		table_types.TRANSACTION_ITEMS,
	}

	for _, tableName := range orderTables {
		tableReader, exists := c.dsManager.Tables[tableName]
		if !exists {
			c.logger.Errorf("Table %s not found in dataset", tableName)
			return nil
		}

		for batch, err := range tableReader.GetTableBatches(c.id, c.maxRows) {
			if err != nil {
				return err
			}

			if err := c.connHandler.Send(*batch, cs_protocol.BATCH); err != nil {
				return err
			}
		}

		endTableMsg := cs_protocol.EndOfTableMessage{
			TableType: tableReader.TableType,
			ClientId:  c.id,
		}
		if err := c.connHandler.Send(endTableMsg, cs_protocol.END_OF_TABLE); err != nil {
			return err
		}
	}

	endDatasetMessage := cs_protocol.EndOfDatasetMessage{
		ClientId: c.id,
	}
	if err := c.connHandler.Send(endDatasetMessage, cs_protocol.END_OF_DATASET); err != nil {
		return err
	}

	c.logger.Infof("Dataset sent, waiting for results...")
	batchCount := 0
	for batchRes, err := range c.connHandler.GetResults() {
		if err != nil {
			c.logger.Errorf("Error receiving batch %d: %v", batchCount, err)
			return err
		}
		batchCount++
		c.logger.Debugf("Received batch %d for table type %s with %d rows", batchCount, batchRes.TableType.String(), len(batchRes.Rows))
		if err := c.resHandler.WriteBatch(batchRes); err != nil {
			c.logger.Errorf("Error writing batch %d for table type %s: %v", batchCount, batchRes.TableType.String(), err)
			return err
		}
		c.logger.Debugf("Successfully wrote batch %d for table type %s", batchCount, batchRes.TableType.String())
	}
	c.resHandler.CloseWriters()
	c.logger.Infof("All results received and written to disk. Total batches processed: %d", batchCount)

	return nil
}
