package gateway

import (
	"net"
	"tp1/middleware"
	e "tp1/pkg/errors"
	cs_protocol "tp1/pkg/protocol/client_server_protocol"
	is_protocol "tp1/pkg/protocol/internal_server_protocol"

	"github.com/google/uuid"
	"github.com/op/go-logging"
)

type Gateway struct {
	logger          *logging.Logger
	producer        *middleware.MessageMiddlewareQueue
	resultsConsumer *middleware.MessageMiddlewareExchange
	conn            net.Conn
}

// TODO: use config instead of hardcoding
// El preprocessor usa el mismo EOT handler que el filter que hicimos ayer. Ese EOT Handler necesitaba
// saber de donde venia el mensaje original del EOT para que, una vez que todos los nodos del cluster
// confirmen que recibieron el EOT, usar esas route keys originales para forwardear el EOT al siguiente.
//
// Pusimos este hashmap hardcodeado para que el gateway agregue las routekeys correspondientes a cada mensaje
// y asi el preprocessor pueda usar el EOT handler sin problemas. Mas tarde usar un CONFIG igual que en el resto.
var routeKeys = map[string][]string{
	"transaction_items": {"transaction_items"},
	"transactions":      {"transactions"},
	"users":             {"users"},
	"stores":            {"stores"},
	"menu_items":        {"menu_items"},
}

func NewGateway(logger *logging.Logger) (*Gateway, error) {
	conn := middleware.GetConnection()
	ch, err := conn.Channel()
	if err != nil {
		return nil, err
	}
	producer, err := middleware.NewQueueProducer("gateway_to_preprocessor", ch)
	if err != nil {
		return nil, err
	}

	route_key := []string{"results_to_gateway"}
	resultsConsumer, err := middleware.NewExchangeConsumer("results_exchange", route_key, "direct", true, ch, "")
	if err != nil {
		return nil, err
	}

	return &Gateway{
		logger:          logger,
		producer:        producer,
		resultsConsumer: resultsConsumer}, nil

}

func (g *Gateway) Start(serverAddress string) error {
	listener, err := net.Listen("tcp", serverAddress)
	if err != nil {
		return err
	}
	defer listener.Close()
	g.logger.Infof("action: start_listener | status: success | addr: %v", listener.Addr())

	for {
		conn, err := listener.Accept()
		g.logger.Infof("action: conn | status: success | cli: %v", conn.LocalAddr())
		if err != nil {
			continue
		}
		g.conn = conn // TODO: adaptar amultiples clientes
		go g.HandleConnection(conn)
	}
}

func (g *Gateway) HandleConnection(conn net.Conn) error {
	g.logger.Infof("action: handshake | status: in_progress | addr: %v", conn.RemoteAddr())

	opCode, _, err := cs_protocol.ReadMessage(conn)
	if err != nil {
		conn.Close()
		return e.FailToReadFromSocketError
	}

	if opCode != cs_protocol.BEGIN {
		conn.Close()
		return e.InvalidOpCodeError
	}

	clientID := uuid.New().String()
	ackMsg := cs_protocol.AckMessage{
		ClientId: clientID,
	}
	if cs_protocol.SendMessage(conn, ackMsg, cs_protocol.ACK) != nil {
		conn.Close()
		return e.FailToSendToSocketError
	}

	g.logger.Infof("action: handshake | status: success | addr: %v", conn.RemoteAddr())
	gog.listenForBatches(conn, clientID)
	g.sendResults()
	return nil
}

func (g *Gateway) listenForBatches(conn net.Conn, clientId string) error {
	for {
		opCode, msgBytes, err := cs_protocol.ReadMessage(conn)
		if err != nil {
			conn.Close()
			return err
		}
		switch opCode {
		case cs_protocol.BATCH:
			g.handleBatches(msgBytes, clientId)
		case cs_protocol.END_OF_TABLE:
			g.handleEndTable(msgBytes, clientId)
		case cs_protocol.END_OF_DATASET:
			g.logger.Debugf("action: read_msg | status: success| client_id: %v |detail: EndDataSet message", clientId)
			return nil // aca bruno
		default:
			conn.Close()
			return e.UnkownServerOpCodeError
		}
	}
}

func (g *Gateway) handleBatches(msgBytes []byte, clientID string) error {
	batchMsg, err := cs_protocol.NewCSMessageFromBytes[cs_protocol.BatchMessage](msgBytes)
	if err != nil {
		return err
	}

	rawMsg := is_protocol.RawBatchMessage{
		TableType: batchMsg.TableType,
		ClientId:  batchMsg.ClientId,
		Rows:      batchMsg.Rows,
	}

	bytesMsg, err := is_protocol.BuildServerMessage(rawMsg, is_protocol.RAW_BATCH, routeKeys[rawMsg.TableType.String()])
	if err != nil {
		return err
	}

	// g.logger.Infof("have a batch of %d lines of table: %v\n", len(rawMsg.Rows), rawMsg.TableType.String())
	return g.sendToPreProcessor(bytesMsg)
}

func (g *Gateway) handleEndTable(msgBytes []byte, clientId string) error {
	csEndMsg, err := cs_protocol.NewCSMessageFromBytes[cs_protocol.EndOfTableMessage](msgBytes)
	if err != nil {
		return err
	}

	isEndMsg := is_protocol.EndOfTableMessage{
		TableType: csEndMsg.TableType,
		ClientId:  csEndMsg.ClientId,
	}

	bytesMsg, err := is_protocol.BuildServerMessage(isEndMsg, is_protocol.END_OF_TABLE, routeKeys[isEndMsg.TableType.String()])
	if err != nil {
		return err
	}

	g.logger.Infof("sending end of table: %s\n", csEndMsg.TableType.String())
	return g.sendToPreProcessor(bytesMsg)
}

func (g *Gateway) sendToPreProcessor(serverMsgBytes []byte) error {
	if err := g.producer.Send(g.producer, serverMsgBytes); err != 0 {
		// TO DO: esto en realidad era un MiddlewareError, ver si queremos devolver ese (tendriamos que implementarle error)
		return e.FailToSendToSocketError
	}
	return nil
}

// ----------- Results Logic --------------

func (g *Gateway) sendResults() {
	defer g.conn.Close() // TODO handle connection accordingly
	done := make(chan struct{})
	err := g.resultsConsumer.StartConsuming(g.resultsConsumer, g.sendResultsCallback)
	if err != 0 {
		g.logger.Errorf("action: create_consumer | status: fail | client_id: %v | error: %v", err)
		return
	}
	<-done
}

func (g *Gateway) sendResultsCallback(consumeChannel middleware.ConsumeChannel, done chan error) {
	defer g.conn.Close() // TODO handle connection accordingly
	eofs := 0
	for d := range *consumeChannel {
		err := d.Ack(false)

		if err != nil {
			g.logger.Error("Error acknowledging message: %v", err)
		}
		msgBytes := d.Body

		opCode, err := is_protocol.GetServerOpCode(msgBytes)
		if err != nil {
			g.logger.Errorf("Could not get opcode: %s\n", err.Error())
			continue
		}
		batchMsg, err := is_protocol.NewServerMessage[is_protocol.BatchMessage](msgBytes)
		if err != nil {
			continue
		}

		csResultMsg := cs_protocol.BatchMessage{
			TableType: batchMsg.TableType,
			Rows:      batchMsg.Rows,
		}

		//TODO: handle multiple clients
		if err := cs_protocol.SendMessage(g.conn, csResultMsg, cs_protocol.RESULTS); err != nil {
			g.logger.Errorf("Could not send message to client: %s\n", err.Error())
		}

		if opCode == is_protocol.END_OF_TABLE {
			eofs++
			g.logger.Infof("Received EOT for %s", batchMsg.TableType.String())
			if eofs == 5 { // TODO: no hardcodear
				g.logger.Infof("Received all EOTs, closing connection")
				return
			}
			g.logger.Infof("NO ENTRO EN EL IF DE 5 EOFS")
		}
	}
	done <- nil
}
